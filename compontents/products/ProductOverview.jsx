"use client";

import {
  useQuery,
  useMutation,
  useQueryClient
} from '@tanstack/react-query'

export default function ProductOverview() {
  // const { data, isLoading, error } = useQuery({
  //   queryKey: ["products"],
  //   queryFn: () => fetch("/api/catalog/get").then((res) => res.json()),
  // });

  const queryClient = useQueryClient()

  const query = useQuery({ queryKey: ['products'], queryFn: () => fetch("/api/catalog/get").then((res) => res.json()) })

  // Mutations
  const mutation = useMutation({
    mutationFn: () => fetch("/api/catalog/get").then((res) => res.json()),
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['products'] })
    },
  })

  return (
    <div>
      <h1>Product Overview</h1>
      <p>{query.data?.length}</p>
    </div>
  )
}