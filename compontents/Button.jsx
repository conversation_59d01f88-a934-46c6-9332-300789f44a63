"use client";

import { But<PERSON> as HeroButton } from "@heroui/react";
import setLanguage from "next-translate/setLanguage";

export default function Button() {
  return (
    <div className="flex flex-wrap gap-4 items-center">
      <HeroButton color="default">Default</HeroButton>
      <HeroButton color="primary">Primary</HeroButton>
      <HeroButton color="secondary">Secondary</HeroButton>
      <HeroButton color="success">Success</HeroButton>
      <HeroButton color="warning">Warning</HeroButton>
      <HeroButton color="danger">Danger</HeroButton>
      <HeroButton color="default" onClick={async () => await setLanguage('en')}>EN</HeroButton>
      <HeroButton color="primary" onClick={async () => await setLanguage('id')}>ID</HeroButton>
    </div>
  );
}