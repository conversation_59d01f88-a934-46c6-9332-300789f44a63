"use client";

import { Button as <PERSON><PERSON>utt<PERSON> } from "@heroui/react";
import { useTranslations, useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';

export default function Button() {
  const t = useTranslations('buttons');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const switchLocale = (newLocale: string) => {
    // Remove the current locale from the pathname and add the new one
    const pathWithoutLocale = pathname.replace(`/${locale}`, '');
    router.push(`/${newLocale}${pathWithoutLocale}`);
  };

  return (
    <div className="flex flex-wrap gap-4 items-center">
      <HeroButton color="default">Default</HeroButton>
      <HeroButton color="primary">Primary</HeroButton>
      <HeroButton color="secondary">Secondary</HeroButton>
      <HeroButton color="success">Success</HeroButton>
      <HeroButton color="warning">Warning</HeroButton>
      <HeroButton color="danger">Danger</HeroButton>
      <HeroButton
        color="default"
        onClick={() => switchLocale('en')}
        variant={locale === 'en' ? 'solid' : 'bordered'}
      >
        {t('switchToEnglish')}
      </HeroButton>
      <HeroButton
        color="primary"
        onClick={() => switchLocale('id')}
        variant={locale === 'id' ? 'solid' : 'bordered'}
      >
        {t('switchToIndonesian')}
      </HeroButton>
    </div>
  );
}