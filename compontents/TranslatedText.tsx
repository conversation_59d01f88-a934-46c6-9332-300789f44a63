"use client";

import { useTranslations } from 'next-intl';

interface TranslatedTextProps {
  keyName: string;
  namespace?: string;
  values?: Record<string, any>;
  className?: string;
}

export default function TranslatedText({ 
  keyName, 
  namespace, 
  values, 
  className,
  ...props 
}: TranslatedTextProps & React.HTMLAttributes<HTMLSpanElement>) {
  const t = useTranslations(namespace);
  
  return (
    <span className={className} {...props}>
      {t(keyName, values)}
    </span>
  );
}
