# Internationalization with next-intl

This project uses [next-intl](https://next-intl.dev/) for internationalization support.

## Overview

The application has been migrated from `next-translate` to `next-intl` for better performance and cleaner implementation.

## Supported Locales

- **English (en)** - Default locale
- **Indonesian (id)**

## Project Structure

```
├── i18n/
│   └── request.js          # next-intl configuration
├── messages/
│   ├── en.json            # English translations
│   └── id.json            # Indonesian translations
├── middleware.js          # Locale detection and routing
├── app/
│   ├── [locale]/          # Internationalized routes
│   │   ├── layout.js      # Locale-specific layout
│   │   └── page.js        # Locale-specific pages
│   └── page.js            # Root page (redirects to default locale)
└── components/
    ├── Button.jsx         # Component with language switcher
    └── TranslatedText.jsx # Reusable translation component
```

## Configuration Files

### i18n/request.js
Contains the main configuration for next-intl, including locale validation and message loading.

### middleware.js
Handles automatic locale detection and routing. Supports both explicit locale paths (`/en/`, `/id/`) and automatic detection.

### messages/
Translation files in JSON format. Each locale has its own file with nested translation keys.

## Usage

### In Server Components
```javascript
import { useTranslations } from 'next-intl';

export default function MyComponent() {
  const t = useTranslations();
  return <h1>{t('welcome')}</h1>;
}
```

### In Client Components
```javascript
"use client";
import { useTranslations } from 'next-intl';

export default function MyComponent() {
  const t = useTranslations('namespace');
  return <span>{t('key')}</span>;
}
```

### Language Switching
The `Button` component includes language switcher buttons that update the URL to change locales.

## Adding New Translations

1. Add the translation key to all locale files in `messages/`
2. Use the translation in your components with `useTranslations()`

Example:
```json
// messages/en.json
{
  "navigation": {
    "home": "Home",
    "about": "About"
  }
}

// messages/id.json
{
  "navigation": {
    "home": "Beranda",
    "about": "Tentang"
  }
}
```

## Migration Notes

- Migrated from `next-translate` to `next-intl`
- Removed cluttered configuration files
- Simplified routing structure
- Improved performance with better caching
- Cleaner component API
