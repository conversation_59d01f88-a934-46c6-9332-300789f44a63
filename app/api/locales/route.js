import { NextResponse } from 'next/server';
import path from 'path';
import { promises as fs } from 'fs';

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const lang = searchParams.get('lang');
  const ns = searchParams.get('ns');

  if (!lang || !ns) {
    return NextResponse.json({}, { status: 400 });
  }

  const filePath = path.join(process.cwd(), 'locales', lang, `${ns}.json`);
  try {
    const fileContents = await fs.readFile(filePath, 'utf8');
    return NextResponse.json(JSON.parse(fileContents));
  } catch (e) {
    return NextResponse.json({}, { status: 404 });
  }
} 