export default async function loadLocaleFrom(lang, ns) {
  // This runs on both server and client, so use fetch
  console.log('Loading locale:', lang, ns); // Add this line
     // ...rest of your code
  const res = await fetch(
    `${
      process.env.NEXT_PUBLIC_BASE_URL || ""
    }/api/locales?lang=${lang}&ns=${ns}`
  );
  if (!res.ok) throw new Error("Failed to load locale");
  return res.json();
}
